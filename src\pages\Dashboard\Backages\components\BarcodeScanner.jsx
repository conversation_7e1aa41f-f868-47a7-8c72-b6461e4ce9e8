import { useState, useEffect, useRef } from 'react';
import { Dialog } from 'primereact/dialog';
import { BrowserMultiFormatReader, BarcodeFormat } from '@zxing/library';
import { motion } from 'framer-motion';
import './BarcodeScanner.css';

// Image preprocessing utilities
const preprocessImage = (canvas, ctx, imageData, mode = 'normal') => {
  const data = imageData.data;
  const width = imageData.width;
  const height = imageData.height;

  switch (mode) {
    case 'contrast':
      // Enhance contrast
      const contrast = 1.5;
      for (let i = 0; i < data.length; i += 4) {
        data[i] = Math.min(255, Math.max(0, contrast * (data[i] - 128) + 128));
        data[i + 1] = Math.min(255, Math.max(0, contrast * (data[i + 1] - 128) + 128));
        data[i + 2] = Math.min(255, Math.max(0, contrast * (data[i + 2] - 128) + 128));
      }
      break;

    case 'grayscale':
      // Convert to grayscale with enhanced contrast
      for (let i = 0; i < data.length; i += 4) {
        const gray = 0.299 * data[i] + 0.587 * data[i + 1] + 0.114 * data[i + 2];
        const enhanced = gray < 128 ? Math.max(0, gray - 30) : Math.min(255, gray + 30);
        data[i] = enhanced;
        data[i + 1] = enhanced;
        data[i + 2] = enhanced;
      }
      break;

    case 'threshold':
      // Binary threshold
      const threshold = 128;
      for (let i = 0; i < data.length; i += 4) {
        const gray = 0.299 * data[i] + 0.587 * data[i + 1] + 0.114 * data[i + 2];
        const binary = gray > threshold ? 255 : 0;
        data[i] = binary;
        data[i + 1] = binary;
        data[i + 2] = binary;
      }
      break;

    case 'sharpen':
      // Sharpen filter
      const sharpenKernel = [0, -1, 0, -1, 5, -1, 0, -1, 0];
      applyConvolution(data, width, height, sharpenKernel);
      break;

    default:
      // Normal mode - slight enhancement
      for (let i = 0; i < data.length; i += 4) {
        data[i] = Math.min(255, data[i] * 1.1);
        data[i + 1] = Math.min(255, data[i + 1] * 1.1);
        data[i + 2] = Math.min(255, data[i + 2] * 1.1);
      }
  }

  ctx.putImageData(imageData, 0, 0);
  return canvas.toDataURL('image/png');
};

const applyConvolution = (data, width, height, kernel) => {
  const output = new Uint8ClampedArray(data);
  const kernelSize = Math.sqrt(kernel.length);
  const half = Math.floor(kernelSize / 2);

  for (let y = half; y < height - half; y++) {
    for (let x = half; x < width - half; x++) {
      let r = 0, g = 0, b = 0;

      for (let ky = 0; ky < kernelSize; ky++) {
        for (let kx = 0; kx < kernelSize; kx++) {
          const px = x + kx - half;
          const py = y + ky - half;
          const idx = (py * width + px) * 4;
          const weight = kernel[ky * kernelSize + kx];

          r += data[idx] * weight;
          g += data[idx + 1] * weight;
          b += data[idx + 2] * weight;
        }
      }

      const idx = (y * width + x) * 4;
      output[idx] = Math.min(255, Math.max(0, r));
      output[idx + 1] = Math.min(255, Math.max(0, g));
      output[idx + 2] = Math.min(255, Math.max(0, b));
    }
  }

  data.set(output);
};

const BarcodeScanner = ({ isOpen, onClose, onBarcodeScanned }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [scannedResult, setScannedResult] = useState('');
  const [scanAttempts, setScanAttempts] = useState(0);
  const [processingMode, setProcessingMode] = useState('normal');
  const videoRef = useRef(null);
  const canvasRef = useRef(null);
  const codeReaderRef = useRef(null);
  const streamRef = useRef(null);
  const scanIntervalRef = useRef(null);
  const frameProcessingRef = useRef(null);

  useEffect(() => {
    if (isOpen) {
      initializeScanner();
    } else {
      cleanup();
    }

    return () => cleanup();
  }, [isOpen]);

  const initializeScanner = async () => {
    setIsLoading(true);
    setError(null);
    setScannedResult('');
    setScanAttempts(0);
    setProcessingMode('normal');

    try {
      // Initialize the code reader
      codeReaderRef.current = new BrowserMultiFormatReader();

      // Get video input devices
      const videoInputDevices = await codeReaderRef.current.listVideoInputDevices();

      if (videoInputDevices.length === 0) {
        throw new Error('No camera devices found');
      }

      // Use the first available camera
      const selectedDeviceId = videoInputDevices[0].deviceId;

      // Get user media
      const stream = await navigator.mediaDevices.getUserMedia({
        video: {
          deviceId: selectedDeviceId,
          width: { ideal: 640 },
          height: { ideal: 480 }
        }
      });

      streamRef.current = stream;

      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        await videoRef.current.play();
      }

      // Start basic scanning first, then enhance if needed
      startBasicScanning(selectedDeviceId);
      setIsLoading(false);
    } catch (err) {
      console.error('Scanner initialization error:', err);

      // Handle specific errors
      if (err.name === 'NotFoundError') {
        // Silently ignore - no camera available
        onClose();
        return;
      } else if (err.name === 'NotAllowedError') {
        setError('Camera access denied. Please allow camera access and try again.');
      } else if (err.name === 'NotReadableError') {
        setError('Camera is already in use by another application.');
      } else {
        setError(err.message || 'Failed to initialize camera');
      }

      setIsLoading(false);
    }
  };

  const startBasicScanning = (deviceId) => {
    let attemptCount = 0;

    // Primary scanning method - direct video scanning
    const primaryScan = () => {
      codeReaderRef.current.decodeFromVideoDevice(
        deviceId,
        videoRef.current,
        (result, error) => {
          if (result) {
            // Check if the result is Code 93 format
            if (result.getBarcodeFormat() === BarcodeFormat.CODE_93) {
              const scannedText = result.getText();
              setScannedResult(scannedText);
              onBarcodeScanned(scannedText);
              cleanup();
              return;
            }
          }

          if (error && error.name !== 'NotFoundException') {
            console.log('Scanning...', error.name);
          }
        }
      );
    };

    // Enhanced scanning with image processing (fallback)
    const enhancedScan = () => {
      if (!videoRef.current || !canvasRef.current) return;

      const canvas = canvasRef.current;
      const ctx = canvas.getContext('2d');
      const video = videoRef.current;

      // Set canvas size to match video
      canvas.width = video.videoWidth || 640;
      canvas.height = video.videoHeight || 480;

      // Draw current video frame
      ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

      try {
        // Try different processing modes
        const modes = ['normal', 'contrast', 'grayscale'];
        const currentMode = modes[attemptCount % modes.length];
        setProcessingMode(currentMode);

        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
        const processedDataUrl = preprocessImage(canvas, ctx, imageData, currentMode);

        // Try to decode from processed image
        codeReaderRef.current.decodeFromImage(undefined, processedDataUrl)
          .then(result => {
            if (result && result.getBarcodeFormat() === BarcodeFormat.CODE_93) {
              const scannedText = result.getText();
              setScannedResult(scannedText);
              onBarcodeScanned(scannedText);
              cleanup();
            }
          })
          .catch(() => {
            // Continue scanning
          });

      } catch (error) {
        console.log('Enhanced scanning error:', error);
      }

      attemptCount++;
      setScanAttempts(attemptCount);
    };

    // Start with primary scanning
    primaryScan();

    // Add enhanced scanning as backup after 3 seconds
    setTimeout(() => {
      if (scanIntervalRef.current === null) {
        scanIntervalRef.current = setInterval(enhancedScan, 500);
      }
    }, 3000);
  };



  const cleanup = () => {
    // Stop scanning intervals
    if (scanIntervalRef.current) {
      clearInterval(scanIntervalRef.current);
      scanIntervalRef.current = null;
    }

    if (frameProcessingRef.current) {
      cancelAnimationFrame(frameProcessingRef.current);
      frameProcessingRef.current = null;
    }

    // Stop the code reader
    if (codeReaderRef.current) {
      codeReaderRef.current.reset();
      codeReaderRef.current = null;
    }

    // Stop the video stream
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
      streamRef.current = null;
    }

    // Clear video element
    if (videoRef.current) {
      videoRef.current.srcObject = null;
    }
  };

  const handleClose = () => {
    cleanup();
    onClose();
  };

  return (
    <Dialog
      header={
        <div className="flex items-center gap-3">
          <div className="w-1 h-8 bg-gradient-to-b from-[#00c3ac] to-[#02aa96] rounded-full"></div>
          <h2 className="text-xl font-semibold text-gray-900">Scan Barcode</h2>
        </div>
      }
      visible={isOpen}
      onHide={handleClose}
      style={{
        width: "90vw",
        maxWidth: "600px",
        borderRadius: "16px",
        overflow: "hidden"
      }}
      modal
      className="barcode-scanner-dialog"
      contentClassName="p-0"
    >
      <div className="p-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          className="space-y-4"
        >
          {/* Instructions */}
          <div className="text-center mb-4">
            <p className="text-gray-600 text-sm">
              Position a Code 93 barcode within the camera view to scan
            </p>
            {scanAttempts > 20 && (
              <div className="mt-2 text-xs text-gray-500">
                Enhanced scanning active - trying different processing modes
              </div>
            )}
          </div>

          {/* Camera View */}
          <div className="relative bg-black rounded-lg overflow-hidden" style={{ aspectRatio: '4/3' }}>
            {isLoading && (
              <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
                <div className="text-center">
                  <div className="w-8 h-8 border-4 border-[#00c3ac] border-t-transparent rounded-full animate-spin mx-auto mb-2"></div>
                  <p className="text-gray-600 text-sm">Initializing camera...</p>
                </div>
              </div>
            )}

            {error && (
              <div className="absolute inset-0 flex items-center justify-center bg-red-50">
                <div className="text-center p-4">
                  <div className="text-red-500 mb-2">
                    <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                    </svg>
                  </div>
                  <p className="text-red-700 text-sm font-medium">{error}</p>
                </div>
              </div>
            )}

            <video
              ref={videoRef}
              className="w-full h-full object-cover"
              playsInline
              muted
            />

            {/* Hidden canvas for image processing */}
            <canvas
              ref={canvasRef}
              className="hidden"
            />

            {/* Scanning overlay */}
            {!isLoading && !error && (
              <div className="absolute inset-0 pointer-events-none">
                <div className="absolute inset-0 border-2 border-[#00c3ac] opacity-50"></div>
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-32 border-2 border-[#00c3ac] rounded-lg">
                  <div className="scan-line"></div>
                </div>

                {/* Processing mode indicator */}
                {processingMode !== 'normal' && scanAttempts > 20 && (
                  <div className="absolute top-2 right-2 bg-[#00c3ac] text-white px-2 py-1 rounded text-xs">
                    Enhanced: {processingMode.toUpperCase()}
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Scanned Result */}
          {scannedResult && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-3">
              <p className="text-green-800 text-sm font-medium">Scanned Result:</p>
              <p className="text-green-700 font-mono text-sm break-all">{scannedResult}</p>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex justify-end gap-3 pt-4 border-t border-gray-200">
            <motion.button
              type="button"
              onClick={handleClose}
              className="px-6 py-3 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg font-medium transition-all duration-200"
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              Close
            </motion.button>
            
            {error && (
              <motion.button
                type="button"
                onClick={initializeScanner}
                className="px-6 py-3 bg-[#00c3ac] hover:bg-[#02aa96] text-white rounded-lg font-medium transition-all duration-200"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                Retry
              </motion.button>
            )}
          </div>
        </motion.div>
      </div>
    </Dialog>
  );
};

export default BarcodeScanner;
