import { useState, useEffect, useRef } from 'react';
import { Dialog } from 'primereact/dialog';
import { BrowserMultiFormatReader, BarcodeFormat, DecodeHintType } from '@zxing/library';
import { motion } from 'framer-motion';
import './BarcodeScanner.css';

// Image preprocessing utilities
const preprocessImage = (canvas, ctx, imageData, mode = 'normal') => {
  const data = imageData.data;
  const width = imageData.width;
  const height = imageData.height;

  switch (mode) {
    case 'contrast':
      // Enhance contrast
      const contrast = 1.5;
      for (let i = 0; i < data.length; i += 4) {
        data[i] = Math.min(255, Math.max(0, contrast * (data[i] - 128) + 128));
        data[i + 1] = Math.min(255, Math.max(0, contrast * (data[i + 1] - 128) + 128));
        data[i + 2] = Math.min(255, Math.max(0, contrast * (data[i + 2] - 128) + 128));
      }
      break;

    case 'grayscale':
      // Convert to grayscale with enhanced contrast
      for (let i = 0; i < data.length; i += 4) {
        const gray = 0.299 * data[i] + 0.587 * data[i + 1] + 0.114 * data[i + 2];
        const enhanced = gray < 128 ? Math.max(0, gray - 30) : Math.min(255, gray + 30);
        data[i] = enhanced;
        data[i + 1] = enhanced;
        data[i + 2] = enhanced;
      }
      break;

    case 'threshold':
      // Binary threshold
      const threshold = 128;
      for (let i = 0; i < data.length; i += 4) {
        const gray = 0.299 * data[i] + 0.587 * data[i + 1] + 0.114 * data[i + 2];
        const binary = gray > threshold ? 255 : 0;
        data[i] = binary;
        data[i + 1] = binary;
        data[i + 2] = binary;
      }
      break;

    case 'sharpen':
      // Sharpen filter
      const sharpenKernel = [0, -1, 0, -1, 5, -1, 0, -1, 0];
      applyConvolution(data, width, height, sharpenKernel);
      break;

    default:
      // Normal mode - slight enhancement
      for (let i = 0; i < data.length; i += 4) {
        data[i] = Math.min(255, data[i] * 1.1);
        data[i + 1] = Math.min(255, data[i + 1] * 1.1);
        data[i + 2] = Math.min(255, data[i + 2] * 1.1);
      }
  }

  ctx.putImageData(imageData, 0, 0);
  return canvas.toDataURL('image/png');
};

const applyConvolution = (data, width, height, kernel) => {
  const output = new Uint8ClampedArray(data);
  const kernelSize = Math.sqrt(kernel.length);
  const half = Math.floor(kernelSize / 2);

  for (let y = half; y < height - half; y++) {
    for (let x = half; x < width - half; x++) {
      let r = 0, g = 0, b = 0;

      for (let ky = 0; ky < kernelSize; ky++) {
        for (let kx = 0; kx < kernelSize; kx++) {
          const px = x + kx - half;
          const py = y + ky - half;
          const idx = (py * width + px) * 4;
          const weight = kernel[ky * kernelSize + kx];

          r += data[idx] * weight;
          g += data[idx + 1] * weight;
          b += data[idx + 2] * weight;
        }
      }

      const idx = (y * width + x) * 4;
      output[idx] = Math.min(255, Math.max(0, r));
      output[idx + 1] = Math.min(255, Math.max(0, g));
      output[idx + 2] = Math.min(255, Math.max(0, b));
    }
  }

  data.set(output);
};

const BarcodeScanner = ({ isOpen, onClose, onBarcodeScanned }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [scannedResult, setScannedResult] = useState('');
  const [scanAttempts, setScanAttempts] = useState(0);
  const [processingMode, setProcessingMode] = useState('normal');
  const videoRef = useRef(null);
  const canvasRef = useRef(null);
  const codeReaderRef = useRef(null);
  const streamRef = useRef(null);
  const scanIntervalRef = useRef(null);
  const frameProcessingRef = useRef(null);

  useEffect(() => {
    if (isOpen) {
      initializeScanner();
    } else {
      cleanup();
    }

    return () => cleanup();
  }, [isOpen]);

  const initializeScanner = async () => {
    setIsLoading(true);
    setError(null);
    setScannedResult('');
    setScanAttempts(0);
    setProcessingMode('normal');

    try {
      // Initialize the code reader with enhanced hints
      codeReaderRef.current = new BrowserMultiFormatReader();

      // Configure decode hints for better accuracy
      const hints = new Map();
      hints.set(DecodeHintType.POSSIBLE_FORMATS, [BarcodeFormat.CODE_93]);
      hints.set(DecodeHintType.TRY_HARDER, true);
      hints.set(DecodeHintType.ALSO_INVERTED, true);

      // Get video input devices
      const videoInputDevices = await codeReaderRef.current.listVideoInputDevices();

      if (videoInputDevices.length === 0) {
        throw new Error('No camera devices found');
      }

      // Use the first available camera with higher resolution
      const selectedDeviceId = videoInputDevices[0].deviceId;

      // Get user media with enhanced settings
      const stream = await navigator.mediaDevices.getUserMedia({
        video: {
          deviceId: selectedDeviceId,
          width: { ideal: 1280, min: 640 },
          height: { ideal: 720, min: 480 },
          focusMode: 'continuous',
          exposureMode: 'continuous'
        }
      });

      streamRef.current = stream;

      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        await videoRef.current.play();
      }

      // Start enhanced scanning process
      startEnhancedScanning(selectedDeviceId, hints);
      setIsLoading(false);
    } catch (err) {
      console.error('Scanner initialization error:', err);

      // Handle specific errors
      if (err.name === 'NotFoundError') {
        // Silently ignore - no camera available
        onClose();
        return;
      } else if (err.name === 'NotAllowedError') {
        setError('Camera access denied. Please allow camera access and try again.');
      } else if (err.name === 'NotReadableError') {
        setError('Camera is already in use by another application.');
      } else {
        setError(err.message || 'Failed to initialize camera');
      }

      setIsLoading(false);
    }
  };

  const startEnhancedScanning = (deviceId, hints) => {
    const processingModes = ['normal', 'contrast', 'grayscale', 'threshold', 'sharpen'];
    let currentModeIndex = 0;
    let attemptCount = 0;

    const scanWithProcessing = () => {
      if (!videoRef.current || !canvasRef.current) return;

      const canvas = canvasRef.current;
      const ctx = canvas.getContext('2d');
      const video = videoRef.current;

      // Set canvas size to match video
      canvas.width = video.videoWidth || 640;
      canvas.height = video.videoHeight || 480;

      // Draw current video frame
      ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

      // Get image data for processing
      const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);

      // Apply current processing mode
      const currentMode = processingModes[currentModeIndex];
      setProcessingMode(currentMode);

      try {
        const processedDataUrl = preprocessImage(canvas, ctx, imageData, currentMode);

        // Try to decode from processed image
        codeReaderRef.current.decodeFromImage(undefined, processedDataUrl)
          .then(result => {
            if (result && result.getBarcodeFormat() === BarcodeFormat.CODE_93) {
              const scannedText = result.getText();
              setScannedResult(scannedText);
              onBarcodeScanned(scannedText);
              cleanup();
              return;
            }
          })
          .catch(() => {
            // Continue scanning with next mode
          });

        // Also try scanning multiple regions of interest
        scanRegionsOfInterest(canvas, ctx, currentMode);

      } catch (error) {
        console.log('Processing error:', error);
      }

      attemptCount++;
      setScanAttempts(attemptCount);

      // Cycle through processing modes every 10 attempts
      if (attemptCount % 10 === 0) {
        currentModeIndex = (currentModeIndex + 1) % processingModes.length;
      }
    };

    // Start continuous scanning
    scanIntervalRef.current = setInterval(scanWithProcessing, 200);
    frameProcessingRef.current = requestAnimationFrame(function processFrame() {
      scanWithProcessing();
      if (scanIntervalRef.current) {
        frameProcessingRef.current = requestAnimationFrame(processFrame);
      }
    });
  };

  const scanRegionsOfInterest = (canvas, ctx, mode) => {
    const width = canvas.width;
    const height = canvas.height;

    // Define regions of interest (center, top, bottom, left, right)
    const regions = [
      { x: width * 0.1, y: height * 0.3, w: width * 0.8, h: height * 0.4 }, // Center horizontal
      { x: width * 0.2, y: height * 0.1, w: width * 0.6, h: height * 0.3 }, // Top
      { x: width * 0.2, y: height * 0.6, w: width * 0.6, h: height * 0.3 }, // Bottom
      { x: width * 0.1, y: height * 0.2, w: width * 0.4, h: height * 0.6 }, // Left
      { x: width * 0.5, y: height * 0.2, w: width * 0.4, h: height * 0.6 }, // Right
    ];

    regions.forEach((region, index) => {
      try {
        const regionImageData = ctx.getImageData(region.x, region.y, region.w, region.h);
        const regionCanvas = document.createElement('canvas');
        regionCanvas.width = region.w;
        regionCanvas.height = region.h;
        const regionCtx = regionCanvas.getContext('2d');

        const processedRegionDataUrl = preprocessImage(regionCanvas, regionCtx, regionImageData, mode);

        codeReaderRef.current.decodeFromImage(undefined, processedRegionDataUrl)
          .then(result => {
            if (result && result.getBarcodeFormat() === BarcodeFormat.CODE_93) {
              const scannedText = result.getText();
              setScannedResult(scannedText);
              onBarcodeScanned(scannedText);
              cleanup();
            }
          })
          .catch(() => {
            // Continue with next region
          });
      } catch (error) {
        // Continue with next region
      }
    });
  };

  const cleanup = () => {
    // Stop scanning intervals
    if (scanIntervalRef.current) {
      clearInterval(scanIntervalRef.current);
      scanIntervalRef.current = null;
    }

    if (frameProcessingRef.current) {
      cancelAnimationFrame(frameProcessingRef.current);
      frameProcessingRef.current = null;
    }

    // Stop the code reader
    if (codeReaderRef.current) {
      codeReaderRef.current.reset();
      codeReaderRef.current = null;
    }

    // Stop the video stream
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
      streamRef.current = null;
    }

    // Clear video element
    if (videoRef.current) {
      videoRef.current.srcObject = null;
    }
  };

  const handleClose = () => {
    cleanup();
    onClose();
  };

  return (
    <Dialog
      header={
        <div className="flex items-center gap-3">
          <div className="w-1 h-8 bg-gradient-to-b from-[#00c3ac] to-[#02aa96] rounded-full"></div>
          <h2 className="text-xl font-semibold text-gray-900">Scan Barcode</h2>
        </div>
      }
      visible={isOpen}
      onHide={handleClose}
      style={{
        width: "90vw",
        maxWidth: "600px",
        borderRadius: "16px",
        overflow: "hidden"
      }}
      modal
      className="barcode-scanner-dialog"
      contentClassName="p-0"
    >
      <div className="p-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          className="space-y-4"
        >
          {/* Instructions */}
          <div className="text-center mb-4">
            <p className="text-gray-600 text-sm">
              Position a Code 93 barcode within the camera view to scan
            </p>
            {scanAttempts > 0 && (
              <div className="mt-2 text-xs text-gray-500">
                Scan attempts: {scanAttempts} | Processing mode: {processingMode}
              </div>
            )}
          </div>

          {/* Camera View */}
          <div className="relative bg-black rounded-lg overflow-hidden" style={{ aspectRatio: '4/3' }}>
            {isLoading && (
              <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
                <div className="text-center">
                  <div className="w-8 h-8 border-4 border-[#00c3ac] border-t-transparent rounded-full animate-spin mx-auto mb-2"></div>
                  <p className="text-gray-600 text-sm">Initializing camera...</p>
                </div>
              </div>
            )}

            {error && (
              <div className="absolute inset-0 flex items-center justify-center bg-red-50">
                <div className="text-center p-4">
                  <div className="text-red-500 mb-2">
                    <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                    </svg>
                  </div>
                  <p className="text-red-700 text-sm font-medium">{error}</p>
                </div>
              </div>
            )}

            <video
              ref={videoRef}
              className="w-full h-full object-cover"
              playsInline
              muted
            />

            {/* Hidden canvas for image processing */}
            <canvas
              ref={canvasRef}
              className="hidden"
            />

            {/* Enhanced scanning overlay */}
            {!isLoading && !error && (
              <div className="absolute inset-0 pointer-events-none">
                {/* Main scanning area */}
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-32 border-2 border-[#00c3ac] rounded-lg">
                  <div className="scan-line"></div>
                </div>

                {/* Region of interest indicators */}
                <div className="absolute top-[10%] left-[20%] w-[60%] h-[30%] border border-[#00c3ac] opacity-30 rounded"></div>
                <div className="absolute top-[60%] left-[20%] w-[60%] h-[30%] border border-[#00c3ac] opacity-30 rounded"></div>
                <div className="absolute top-[20%] left-[10%] w-[40%] h-[60%] border border-[#00c3ac] opacity-30 rounded"></div>
                <div className="absolute top-[20%] left-[50%] w-[40%] h-[60%] border border-[#00c3ac] opacity-30 rounded"></div>

                {/* Processing mode indicator */}
                {processingMode !== 'normal' && (
                  <div className="absolute top-2 right-2 bg-[#00c3ac] text-white px-2 py-1 rounded text-xs">
                    {processingMode.toUpperCase()}
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Scanned Result */}
          {scannedResult && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-3">
              <p className="text-green-800 text-sm font-medium">Scanned Result:</p>
              <p className="text-green-700 font-mono text-sm break-all">{scannedResult}</p>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex justify-end gap-3 pt-4 border-t border-gray-200">
            <motion.button
              type="button"
              onClick={handleClose}
              className="px-6 py-3 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg font-medium transition-all duration-200"
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              Close
            </motion.button>
            
            {error && (
              <motion.button
                type="button"
                onClick={initializeScanner}
                className="px-6 py-3 bg-[#00c3ac] hover:bg-[#02aa96] text-white rounded-lg font-medium transition-all duration-200"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                Retry
              </motion.button>
            )}
          </div>
        </motion.div>
      </div>
    </Dialog>
  );
};

export default BarcodeScanner;
