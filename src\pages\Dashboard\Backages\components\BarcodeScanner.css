/* Barcode Scanner Styles */
.barcode-scanner-dialog .p-dialog-content {
  padding: 0 !important;
}

.barcode-scanner-dialog .p-dialog-header {
  border-bottom: 1px solid #e5e7eb;
  padding: 1.5rem;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .barcode-scanner-dialog {
    width: 95vw !important;
    margin: 1rem;
  }
  
  .barcode-scanner-dialog .p-dialog-header {
    padding: 1rem;
  }
}

/* Video element styling */
.barcode-scanner-dialog video {
  background-color: #000;
}

/* Scanning animation */
@keyframes scan-line {
  0% {
    top: 0;
  }
  100% {
    top: 100%;
  }
}

.scan-line {
  position: absolute;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, #00c3ac, transparent);
  animation: scan-line 2s linear infinite;
}

/* Processing mode indicator */
.barcode-scanner-dialog .absolute.top-2.right-2 {
  backdrop-filter: blur(4px);
  font-weight: 600;
  letter-spacing: 0.5px;
}
