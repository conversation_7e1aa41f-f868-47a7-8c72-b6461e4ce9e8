/* Barcode Scanner Styles */
.barcode-scanner-dialog .p-dialog-content {
  padding: 0 !important;
}

.barcode-scanner-dialog .p-dialog-header {
  border-bottom: 1px solid #e5e7eb;
  padding: 1.5rem;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .barcode-scanner-dialog {
    width: 95vw !important;
    margin: 1rem;
  }
  
  .barcode-scanner-dialog .p-dialog-header {
    padding: 1rem;
  }
}

/* Video element styling */
.barcode-scanner-dialog video {
  background-color: #000;
}

/* Enhanced scanning animations */
@keyframes scan-line {
  0% {
    top: 0;
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
  100% {
    top: 100%;
    opacity: 1;
  }
}

@keyframes pulse-border {
  0% {
    border-color: #00c3ac;
    box-shadow: 0 0 0 0 rgba(0, 195, 172, 0.7);
  }
  50% {
    border-color: #02aa96;
    box-shadow: 0 0 0 4px rgba(0, 195, 172, 0.3);
  }
  100% {
    border-color: #00c3ac;
    box-shadow: 0 0 0 0 rgba(0, 195, 172, 0.7);
  }
}

.scan-line {
  position: absolute;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, transparent, #00c3ac, #82f570, #00c3ac, transparent);
  animation: scan-line 2s ease-in-out infinite;
  border-radius: 2px;
}

/* Enhanced scanning overlay styles */
.barcode-scanner-dialog .absolute.top-1\/2 {
  animation: pulse-border 2s ease-in-out infinite;
}

/* Processing mode indicator */
.barcode-scanner-dialog .absolute.top-2.right-2 {
  backdrop-filter: blur(4px);
  font-weight: 600;
  letter-spacing: 0.5px;
}

/* Region indicators fade animation */
@keyframes fade-regions {
  0%, 100% {
    opacity: 0.2;
  }
  50% {
    opacity: 0.4;
  }
}

.barcode-scanner-dialog .absolute.border.border-\[#00c3ac\].opacity-30 {
  animation: fade-regions 3s ease-in-out infinite;
}
